// DigitalVitrine - JavaScript commun

document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const navMenu = document.querySelector('.nav-menu');
    
    if (mobileMenuBtn) {
        mobileMenuBtn.addEventListener('click', function() {
            navMenu.classList.toggle('active');
        });
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Form handling
    const contactForm = document.querySelector('#contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            // Validate form
            if (validateForm(data)) {
                // Simulate form submission
                showSuccessMessage();
                this.reset();
            }
        });
    }

    // Form validation
    function validateForm(data) {
        const requiredFields = ['name', 'business', 'email', 'business-type'];
        let isValid = true;
        
        // Remove previous error messages
        document.querySelectorAll('.error-message').forEach(msg => msg.remove());
        
        requiredFields.forEach(field => {
            if (!data[field] || data[field].trim() === '') {
                showFieldError(field, 'Ce champ est requis');
                isValid = false;
            }
        });
        
        // Email validation
        if (data.email && !isValidEmail(data.email)) {
            showFieldError('email', 'Veuillez entrer une adresse email valide');
            isValid = false;
        }
        
        return isValid;
    }

    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function showFieldError(fieldName, message) {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (field) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.style.color = '#dc2626';
            errorDiv.style.fontSize = '0.875rem';
            errorDiv.style.marginTop = '0.25rem';
            errorDiv.textContent = message;
            
            field.parentNode.appendChild(errorDiv);
            field.style.borderColor = '#dc2626';
        }
    }

    function showSuccessMessage() {
        const successDiv = document.createElement('div');
        successDiv.className = 'success-message';
        successDiv.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 1rem 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            z-index: 1000;
            animation: slideIn 0.3s ease;
        `;
        successDiv.textContent = 'Votre demande a été envoyée avec succès !';
        
        document.body.appendChild(successDiv);
        
        setTimeout(() => {
            successDiv.remove();
        }, 5000);
    }

    // Animation on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for animation
    document.querySelectorAll('.card, .module-card').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });

    // Counter animation for stats
    function animateCounters() {
        const counters = document.querySelectorAll('.stat-number');
        
        counters.forEach(counter => {
            const target = counter.textContent;
            const isPercentage = target.includes('%');
            const isPlus = target.includes('+');
            const isMinus = target.includes('-');
            const isTime = target.includes('h/24');

            // Extract numeric value
            let numericValue;
            if (isTime) {
                numericValue = 24;
            } else {
                const matches = target.match(/-?\d+/);
                numericValue = matches ? Math.abs(parseInt(matches[0])) : 0;
            }

            if (numericValue) {
                let current = 0;
                const increment = numericValue / 50;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= numericValue) {
                        current = numericValue;
                        clearInterval(timer);
                    }

                    let displayValue = Math.floor(current);
                    if (isTime) {
                        displayValue = Math.floor(current) + 'h/24';
                    } else {
                        const prefix = isMinus ? '-' : (isPlus ? '+' : '');
                        displayValue = prefix + displayValue + (isPercentage ? '%' : '');
                    }

                    counter.textContent = displayValue;
                }, 50);
            }
        });
    }

    // Trigger counter animation when stats section is visible
    const statsSection = document.querySelector('.benefits-stats');
    if (statsSection) {
        const statsObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounters();
                    statsObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        statsObserver.observe(statsSection);
    }

    // Module comparison functionality
    window.compareModules = function() {
        const checkboxes = document.querySelectorAll('.module-checkbox:checked');
        const selectedModules = Array.from(checkboxes).map(cb => cb.value);
        
        if (selectedModules.length === 0) {
            alert('Veuillez sélectionner au moins un module à comparer');
            return;
        }
        
        // Redirect to comparison page with selected modules
        const params = new URLSearchParams();
        selectedModules.forEach(module => params.append('modules', module));
        window.location.href = `comparison.html?${params.toString()}`;
    };

    // Price calculator
    window.calculatePrice = function() {
        const basePrice = 10;
        let totalPrice = basePrice;
        
        const modulesPrices = {
            'commande': 15,
            'communication': 8,
            'google': 5,
            'traiteur': 12,
            'origine': 10
        };
        
        const selectedModules = document.querySelectorAll('.module-checkbox:checked');
        selectedModules.forEach(checkbox => {
            const modulePrice = modulesPrices[checkbox.value];
            if (modulePrice) {
                totalPrice += modulePrice;
            }
        });
        
        // Apply pack discounts
        const selectedCount = selectedModules.length;
        if (selectedCount >= 4) {
            totalPrice = Math.round(totalPrice * 0.75); // 25% discount for complete pack
        } else if (selectedCount >= 2) {
            totalPrice = Math.round(totalPrice * 0.85); // 15% discount for multiple modules
        }
        
        document.querySelector('#total-price').textContent = totalPrice + '€/mois';
    };
});

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .mobile-menu-btn {
        display: none;
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: var(--text-primary);
    }
    
    @media (max-width: 768px) {
        .mobile-menu-btn {
            display: block;
        }
        
        .nav-menu {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--white);
            flex-direction: column;
            padding: 1rem;
            box-shadow: var(--shadow-lg);
            transform: translateY(-100%);
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        
        .nav-menu.active {
            transform: translateY(0);
            opacity: 1;
            visibility: visible;
        }
    }
`;
document.head.appendChild(style);

function calculatePrice() {
    let totalPrice = 10; // Base module price
    const commandeCheckbox = document.getElementById("commande");
    const communicationCheckbox = document.getElementById("communication");
    const googleCheckbox = document.getElementById("google");
    const traiteurCheckbox = document.getElementById("traiteur");
    const origineCheckbox = document.getElementById("origine");

    if (commandeCheckbox.checked) {
        totalPrice += 15;
    }
    if (communicationCheckbox.checked) {
        totalPrice += 8;
    }
    if (googleCheckbox.checked) {
        totalPrice += 5;
    }
    if (traiteurCheckbox.checked) {
        totalPrice += 12;
    }
    if (origineCheckbox.checked) {
        totalPrice += 10;
    }

    document.getElementById("total-price").innerText = totalPrice + "€/mois";
}
